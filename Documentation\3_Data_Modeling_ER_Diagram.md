# 🗄️ Data Modeling & ER Diagram - BookNGO Bus Booking System

## 1. Entity Identification

### Core Entities

#### 1.1 User
- **Purpose:** Represents all system users (Customers, Operators, Admins)
- **Key Attributes:** id, name, email, phone, role, company_name (for operators)
- **Business Rules:** Role-based access control, unique email/phone

#### 1.2 City
- **Purpose:** Represents cities/locations for routes
- **Key Attributes:** id, name, province, district, latitude, longitude
- **Business Rules:** Unique city names within districts

#### 1.3 Route
- **Purpose:** Defines travel routes between cities
- **Key Attributes:** id, name, source_city_id, destination_city_id, distance_km, base_fare
- **Business Rules:** Source and destination must be different cities

#### 1.4 Bus Type
- **Purpose:** Categorizes buses by type and layout
- **Key Attributes:** id, name, total_seats, seat_layout, base_fare_multiplier
- **Business Rules:** Seat layout defines arrangement (2x2, 1x2, 3x2)

#### 1.5 Bus
- **Purpose:** Represents individual buses in operator fleets
- **Key Attributes:** id, bus_number, operator_id, bus_type_id, license_plate, total_seats
- **Business Rules:** Unique bus numbers per operator, seat layout from bus type

#### 1.6 Seat
- **Purpose:** Individual seats within buses
- **Key Attributes:** id, bus_id, seat_number, row_number, column_number, seat_type
- **Business Rules:** Seat numbering follows layout restrictions

#### 1.7 Schedule
- **Purpose:** Scheduled trips for specific routes and dates
- **Key Attributes:** id, bus_id, route_id, travel_date, departure_time, fare, available_seats
- **Business Rules:** One bus per schedule, fare can override route base fare

#### 1.8 Booking
- **Purpose:** Customer reservations for bus seats
- **Key Attributes:** id, booking_reference, user_id, schedule_id, seat_numbers, total_amount, status
- **Business Rules:** Unique booking reference, seat numbers as JSON array

#### 1.9 Payment
- **Purpose:** Payment transactions for bookings
- **Key Attributes:** id, booking_id, amount, payment_method, transaction_id, status
- **Business Rules:** One successful payment per booking

#### 1.10 Seat Reservation
- **Purpose:** Temporary seat holds during booking process
- **Key Attributes:** id, user_id, schedule_id, seat_numbers, expires_at, status
- **Business Rules:** 1-hour expiry, automatic cleanup

## 2. Entity Relationship Diagram

```mermaid
erDiagram
    %% Core Entities
    USER {
        bigint id PK
        string name
        string email UK
        string phone UK
        string password
        enum role
        boolean is_active
        string company_name
        string company_address
        string company_license
        timestamp created_at
        timestamp updated_at
    }

    CITY {
        bigint id PK
        string name
        string province
        string district
        decimal latitude
        decimal longitude
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    ROUTE {
        bigint id PK
        string name
        bigint source_city_id FK
        bigint destination_city_id FK
        decimal distance_km
        decimal base_fare
        time estimated_duration
        json stops
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    BUS_TYPE {
        bigint id PK
        string name
        text description
        integer total_seats
        json seat_layout
        decimal base_fare_multiplier
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    BUS {
        bigint id PK
        string bus_number
        bigint operator_id FK
        bigint bus_type_id FK
        string license_plate
        string model
        string color
        integer manufacture_year
        integer total_seats
        json seat_layout
        json amenities
        text description
        enum status
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    SEAT {
        bigint id PK
        bigint bus_id FK
        string seat_number
        integer row_number
        integer column_number
        enum seat_type
        boolean is_window
        boolean is_aisle
        boolean is_available
        timestamp created_at
        timestamp updated_at
    }

    SCHEDULE {
        bigint id PK
        bigint bus_id FK
        bigint route_id FK
        bigint operator_id FK
        date travel_date
        time departure_time
        time arrival_time
        decimal fare
        integer available_seats
        enum status
        text notes
        timestamp created_at
        timestamp updated_at
    }

    BOOKING {
        bigint id PK
        string booking_reference UK
        bigint user_id FK
        bigint schedule_id FK
        json seat_numbers
        integer passenger_count
        decimal total_amount
        enum status
        json passenger_details
        string contact_phone
        string contact_email
        timestamp booking_expires_at
        text special_requests
        enum booking_type
        enum payment_method
        enum payment_status
        bigint booked_by FK
        timestamp cancelled_at
        text cancellation_reason
        timestamp created_at
        timestamp updated_at
    }

    PAYMENT {
        bigint id PK
        bigint booking_id FK
        decimal amount
        enum payment_method
        string transaction_id
        string gateway_response
        enum status
        timestamp processed_at
        text failure_reason
        timestamp created_at
        timestamp updated_at
    }

    SEAT_RESERVATION {
        bigint id PK
        bigint user_id FK
        bigint schedule_id FK
        json seat_numbers
        enum status
        timestamp expires_at
        timestamp notified_at
        timestamp created_at
        timestamp updated_at
    }

    %% Relationships
    USER ||--o{ BUS : "operates"
    USER ||--o{ SCHEDULE : "creates"
    USER ||--o{ BOOKING : "makes"
    USER ||--o{ SEAT_RESERVATION : "reserves"
    USER ||--o{ BOOKING : "books_for"

    CITY ||--o{ ROUTE : "source"
    CITY ||--o{ ROUTE : "destination"

    ROUTE ||--o{ SCHEDULE : "scheduled_on"

    BUS_TYPE ||--o{ BUS : "categorizes"

    BUS ||--o{ SEAT : "contains"
    BUS ||--o{ SCHEDULE : "assigned_to"

    SCHEDULE ||--o{ BOOKING : "booked_for"
    SCHEDULE ||--o{ SEAT_RESERVATION : "reserved_for"

    BOOKING ||--o{ PAYMENT : "paid_through"
```

## 3. Entity Descriptions

### 3.1 User Entity
**Purpose:** Central entity for all system users with role-based differentiation

**Attributes:**
- `id`: Primary key, auto-increment
- `name`: Full name of the user
- `email`: Unique email address for authentication
- `phone`: Unique phone number for contact and authentication
- `role`: Enum (customer, operator, admin)
- `company_name`: Required for operators, company name
- `company_license`: Business license number for operators
- `is_active`: Boolean flag for account status

**Relationships:**
- One-to-Many with Bus (as operator)
- One-to-Many with Schedule (as operator)
- One-to-Many with Booking (as customer)
- One-to-Many with Seat Reservation (as customer)

### 3.2 Route Entity
**Purpose:** Defines travel routes between cities with pricing and distance information

**Attributes:**
- `source_city_id`: Foreign key to source city
- `destination_city_id`: Foreign key to destination city
- `distance_km`: Route distance in kilometers
- `base_fare`: Base fare for the route
- `estimated_duration`: Expected travel time
- `stops`: JSON array of intermediate stops

**Business Rules:**
- Source and destination cities must be different
- Base fare used as default, can be overridden in schedules
- Stops are optional intermediate locations

### 3.3 Bus Entity
**Purpose:** Represents individual buses with their specifications and seat layouts

**Attributes:**
- `operator_id`: Foreign key to operator (User)
- `bus_type_id`: Foreign key to bus type
- `seat_layout`: JSON structure defining seat arrangement
- `total_seats`: Total number of seats in the bus
- `amenities`: JSON array of bus features (AC, WiFi, etc.)
- `status`: Enum (active, maintenance, inspection, retired)

**Business Rules:**
- Bus number must be unique per operator
- Seat layout inherited from bus type but can be customized
- Total seats must match seat layout configuration

### 3.4 Schedule Entity
**Purpose:** Represents scheduled trips with specific dates, times, and pricing

**Attributes:**
- `travel_date`: Date of travel
- `departure_time`: Scheduled departure time
- `arrival_time`: Expected arrival time
- `fare`: Trip fare (can override route base fare)
- `available_seats`: Current available seat count
- `status`: Enum (scheduled, cancelled, completed, in_progress)

**Business Rules:**
- One bus can have multiple schedules for different dates
- Available seats updated automatically based on bookings
- Fare can be different from route base fare (festival pricing)

### 3.5 Booking Entity
**Purpose:** Customer reservations with passenger details and payment information

**Attributes:**
- `booking_reference`: Unique booking identifier (BNG-XXXXXXXX)
- `seat_numbers`: JSON array of booked seat numbers
- `passenger_details`: JSON object with passenger information
- `booking_type`: Enum (online, counter)
- `payment_status`: Enum (pending, completed, failed, refunded)
- `booking_expires_at`: Expiry time for unpaid bookings

**Business Rules:**
- Maximum 10 seats per booking
- Booking reference auto-generated with BNG prefix
- Expires after 1 hour if payment not completed
- Seat numbers must be available for the schedule

## 4. Data Integrity Constraints

### 4.1 Primary Key Constraints
- All entities have auto-incrementing bigint primary keys
- Primary keys ensure unique record identification

### 4.2 Foreign Key Constraints
- All foreign keys have CASCADE DELETE where appropriate
- Referential integrity maintained across all relationships
- Orphaned records prevented through proper constraints

### 4.3 Unique Constraints
- User email and phone must be unique
- Booking reference must be unique
- Bus number unique per operator
- Route name unique per source-destination pair

### 4.4 Check Constraints
- Seat numbers must be positive integers
- Fare amounts must be positive
- Travel date cannot be in the past
- Passenger count must match seat count

### 4.5 Business Rule Constraints
- Source and destination cities must be different
- Booking expiry time must be after creation time
- Available seats cannot be negative
- Payment amount must match booking total

## 5. Indexing Strategy

### 5.1 Primary Indexes
- Primary key indexes on all id columns
- Unique indexes on email, phone, booking_reference

### 5.2 Foreign Key Indexes
- Indexes on all foreign key columns for join performance
- Composite indexes on frequently queried combinations

### 5.3 Query Optimization Indexes
- Index on (source_city_id, destination_city_id) for route searches
- Index on (travel_date, status) for schedule searches
- Index on (schedule_id, status) for booking queries
- Index on (expires_at, status) for reservation cleanup

### 5.4 Performance Indexes
- Composite index on (operator_id, travel_date) for operator dashboards
- Index on (user_id, created_at) for booking history
- Index on (payment_status, created_at) for payment reports

## 6. Data Storage Considerations

### 6.1 JSON Fields
- `seat_layout`: Stores seat arrangement configuration
- `seat_numbers`: Array of booked seat numbers
- `passenger_details`: Flexible passenger information storage
- `amenities`: Bus features and facilities
- `stops`: Route intermediate stops

### 6.2 Enum Fields
- `role`: (customer, operator, admin)
- `status`: Various status fields with predefined values
- `payment_method`: (esewa, khalti, fonepay, ime_pay, cash)
- `booking_type`: (online, counter)

### 6.3 Timestamp Fields
- `created_at` and `updated_at` on all entities
- `expires_at` for time-sensitive records
- `cancelled_at` for soft deletion tracking

## 7. Data Archival Strategy

### 7.1 Historical Data
- Completed bookings archived after 2 years
- Payment records retained for 5 years for audit
- Schedule data archived after completion

### 7.2 Cleanup Procedures
- Expired reservations cleaned up hourly
- Failed payment records cleaned up after 30 days
- Cancelled bookings archived after 6 months

### 7.3 Backup Strategy
- Daily incremental backups
- Weekly full database backups
- Point-in-time recovery capability
- Offsite backup storage for disaster recovery
