<?php

namespace App\Providers;

use App\Events\BookingStatusUpdated;
use App\Events\SeatReserved;
use App\Events\SeatReservationExpired;
use App\Listeners\SendOperatorNotification;
use App\Listeners\SendOperatorSeatReservationNotification;
use App\Listeners\SendOperatorSeatExpiryNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        BookingStatusUpdated::class => [
            SendOperatorNotification::class,
        ],
        SeatReserved::class => [
            SendOperatorSeatReservationNotification::class,
        ],
        SeatReservationExpired::class => [
            SendOperatorSeatExpiryNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
