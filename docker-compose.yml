version: '3.8'

services:
  # Application service
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: bookngo-app
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./storage:/var/www/html/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
      - app-logs:/var/log
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=db
      - DB_DATABASE=bookngo
      - DB_USERNAME=bookngo_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - db
      - redis
    networks:
      - bookngo-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Database service
  db:
    image: mysql:8.0
    container_name: bookngo-db
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: bookngo
      MYSQL_USER: bookngo_user
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    volumes:
      - db-data:/var/lib/mysql
      - ./docker/mysql.cnf:/etc/mysql/conf.d/custom.cnf
    networks:
      - bookngo-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis service
  redis:
    image: redis:7-alpine
    container_name: bookngo-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./docker/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - bookngo-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (for multiple app instances)
  nginx-lb:
    image: nginx:alpine
    container_name: bookngo-nginx-lb
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./docker/nginx-lb.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - app
    networks:
      - bookngo-network
    profiles:
      - load-balancer

  # Elasticsearch for search functionality
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: bookngo-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - bookngo-network
    profiles:
      - search

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: bookngo-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - bookngo-network
    profiles:
      - monitoring

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: bookngo-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - bookngo-network
    profiles:
      - monitoring

  # Backup service
  backup:
    image: alpine:latest
    container_name: bookngo-backup
    restart: "no"
    volumes:
      - db-data:/backup/db-data:ro
      - ./storage:/backup/storage:ro
      - backup-data:/backup/output
      - ./docker/backup.sh:/backup.sh
    networks:
      - bookngo-network
    profiles:
      - backup
    command: /backup.sh

volumes:
  db-data:
    driver: local
  redis-data:
    driver: local
  elasticsearch-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  backup-data:
    driver: local
  app-logs:
    driver: local

networks:
  bookngo-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
