/* Real-time Dashboard Styles */

.dashboard-container {
    padding: 20px;
    background: #f8fafc;
    min-height: 100vh;
}

.dashboard-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
}

.last-updated {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

/* Stats Grid */
#dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.stat-updated {
    animation: statPulse 2s ease;
}

.stat-label {
    font-size: 14px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    transition: all 0.3s ease;
}

.stat-value.stat-changed {
    animation: valueChange 1s ease;
}

@keyframes statPulse {
    0%, 100% {
        background: white;
    }
    50% {
        background: #f0f9ff;
        border-color: #3b82f6;
    }
}

@keyframes valueChange {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        color: #3b82f6;
    }
}

/* Chart Container */
#dashboard-chart {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.chart-container {
    width: 100%;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 4px;
    height: 200px;
    padding: 10px 0;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, #3b82f6, #60a5fa);
    border-radius: 4px 4px 0 0;
    position: relative;
    min-height: 2px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-bar:hover {
    background: linear-gradient(to top, #2563eb, #3b82f6);
    transform: scaleY(1.05);
}

.bar-value {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.chart-bar:hover .bar-value {
    opacity: 1;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: slideInRight 0.3s ease, fadeOut 0.3s ease 4.7s;
    border-left: 4px solid #3b82f6;
}

.notification-info {
    border-left-color: #3b82f6;
}

.notification-success {
    border-left-color: #10b981;
}

.notification-warning {
    border-left-color: #f59e0b;
}

.notification-error {
    border-left-color: #ef4444;
}

.notification-content {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-message {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    font-weight: 700;
    color: #9ca3af;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #374151;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Notification Badge */
#notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    font-weight: 600;
    display: none;
    align-items: center;
    justify-content: center;
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Loading States */
.stat-card.loading {
    position: relative;
}

.stat-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 15px;
    }
    
    #dashboard-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-value {
        font-size: 28px;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .dashboard-title {
        font-size: 24px;
    }
    
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .chart-bars {
        height: 150px;
    }
}

@media (max-width: 480px) {
    .stat-card {
        padding: 16px;
    }
    
    .stat-value {
        font-size: 24px;
    }
    
    .chart-title {
        font-size: 16px;
    }
    
    .chart-bars {
        height: 120px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dashboard-container {
        background: #111827;
    }
    
    .stat-card,
    #dashboard-chart,
    .dashboard-header {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .stat-label {
        color: #9ca3af;
    }
    
    .stat-value {
        color: #f9fafb;
    }
    
    .dashboard-title {
        color: #f9fafb;
    }
    
    .notification {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .notification-message {
        color: #f9fafb;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .stat-card {
        border: 2px solid #000;
    }
    
    .chart-bar {
        background: #000;
    }
    
    .notification {
        border: 2px solid #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .stat-card,
    .stat-value,
    .chart-bar,
    .notification {
        transition: none;
        animation: none;
    }
    
    .stat-card:hover {
        transform: none;
    }
    
    .chart-bar:hover {
        transform: none;
    }
}
