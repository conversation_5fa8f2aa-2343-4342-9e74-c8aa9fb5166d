<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpine.js Test</title>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .dropdown { position: relative; display: inline-block; }
        .dropdown-menu { 
            position: absolute; 
            top: 100%; 
            left: 0; 
            background: white; 
            border: 1px solid #ccc; 
            padding: 10px; 
            min-width: 200px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button { 
            padding: 10px 15px; 
            background: #3b82f6; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        button:hover { background: #2563eb; }
        .debug { background: #f3f4f6; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Alpine.js Navbar Component Test</h1>
    
    <div class="debug">
        <h3>Debug Info:</h3>
        <p id="alpine-status">Checking Alpine.js...</p>
        <p id="component-status">Checking component...</p>
    </div>
    
    <!-- Test the exact same component structure as in the navbar -->
    <div x-data="navbarDropdowns" x-init="console.log('Component initialized:', $data)">
        <div class="dropdown">
            <button @click="toggleUserDropdown()" type="button">
                User Menu (Open: <span x-text="userDropdownOpen"></span>)
            </button>
            
            <div x-show="userDropdownOpen" 
                 @click.away="userDropdownOpen = false"
                 x-transition
                 class="dropdown-menu">
                <p>User dropdown content</p>
                <button @click="closeAllDropdowns()">Close All</button>
            </div>
        </div>
        
        <div class="dropdown" style="margin-left: 20px;">
            <button @click="toggleNotificationDropdown()" type="button">
                Notifications (Open: <span x-text="notificationOpen"></span>)
            </button>
            
            <div x-show="notificationOpen" 
                 @click.away="notificationOpen = false"
                 x-transition
                 class="dropdown-menu">
                <p>Notification dropdown content</p>
                <button @click="closeAllDropdowns()">Close All</button>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button @click="toggleMobileMenu()" type="button">
                Mobile Menu (Open: <span x-text="mobileOpen"></span>)
            </button>
        </div>
        
        <div class="debug" style="margin-top: 20px;">
            <h4>Component State:</h4>
            <p>User Dropdown: <span x-text="userDropdownOpen"></span></p>
            <p>Notification Dropdown: <span x-text="notificationOpen"></span></p>
            <p>Mobile Menu: <span x-text="mobileOpen"></span></p>
        </div>
    </div>
    
    <script>
        // Define the same component as in navbar-dropdowns.js
        document.addEventListener('alpine:init', () => {
            console.log('🎯 Alpine init event fired');
            
            Alpine.data('navbarDropdowns', () => ({
                userDropdownOpen: false,
                notificationOpen: false,
                mobileOpen: false,

                init() {
                    console.log('✅ Navbar dropdowns component initialized');
                    document.getElementById('component-status').textContent = 'Component initialized successfully!';
                },

                closeAllDropdowns() {
                    console.log('🔒 Closing all dropdowns');
                    this.userDropdownOpen = false;
                    this.notificationOpen = false;
                },

                toggleUserDropdown() {
                    console.log('👤 Toggling user dropdown');
                    this.notificationOpen = false;
                    this.userDropdownOpen = !this.userDropdownOpen;
                },

                toggleNotificationDropdown() {
                    console.log('🔔 Toggling notification dropdown');
                    this.userDropdownOpen = false;
                    this.notificationOpen = !this.notificationOpen;
                },

                toggleMobileMenu() {
                    console.log('📱 Toggling mobile menu');
                    this.mobileOpen = !this.mobileOpen;
                    if (this.mobileOpen) {
                        this.closeAllDropdowns();
                    }
                }
            }));
            
            console.log('🚀 Component registered');
        });
        
        // Check Alpine.js status
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const status = document.getElementById('alpine-status');
                if (typeof Alpine !== 'undefined') {
                    status.textContent = `Alpine.js loaded successfully! Version: ${Alpine.version || 'unknown'}`;
                    status.style.color = 'green';
                } else {
                    status.textContent = 'Alpine.js not found!';
                    status.style.color = 'red';
                }
            }, 100);
        });
    </script>
</body>
</html>
