# 🎯 Use Case Diagram - BookNGO Bus Booking System

## Use Case Diagram

The following diagram illustrates the main use cases and actors in the BookNGO system:

```mermaid
graph TB
    %% Actors
    Customer[👤 Customer]
    Operator[🏢 Bus Operator]
    Admin[👨‍💼 Admin]
    PaymentGW[💳 Payment Gateway]
    NotificationSvc[📧 Notification Service]
    System[⚙️ System]

    %% Customer Use Cases
    subgraph "Customer Use Cases"
        UC001[UC-001: Register/Login]
        UC002[UC-002: Search Buses]
        UC003[UC-003: View Bus Details]
        UC004[UC-004: Select Seats]
        UC005[UC-005: Make Payment]
        UC006[UC-006: Download Ticket]
        UC007[UC-007: View Booking History]
        UC008[UC-008: Cancel Booking]
        UC009[UC-009: Reserve Seats]
        UC010[UC-010: Receive Notifications]
    end

    %% Operator Use Cases
    subgraph "Operator Use Cases"
        UC011[UC-011: Manage Bus Fleet]
        UC012[UC-012: Create Schedules]
        UC013[UC-013: View Bookings]
        UC014[UC-014: Counter Booking]
        UC015[UC-015: Manage Seat Availability]
        UC016[UC-016: Generate Reports]
        UC017[UC-017: Block/Unblock Seats]
    end

    %% Admin Use Cases
    subgraph "Admin Use Cases"
        UC018[UC-018: Manage Operators]
        UC019[UC-019: Manage Users]
        UC020[UC-020: Monitor Bookings]
        UC021[UC-021: System Reports]
        UC022[UC-022: Manage Routes]
        UC023[UC-023: System Config]
        UC024[UC-024: Festival Mode]
    end

    %% System Use Cases
    subgraph "System Use Cases"
        UC025[UC-025: Auto-expire Reservations]
        UC026[UC-026: Send Notifications]
        UC027[UC-027: Process Payments]
        UC028[UC-028: Generate References]
        UC029[UC-029: Update Availability]
    end

    %% Customer Relationships
    Customer --> UC001
    Customer --> UC002
    Customer --> UC003
    Customer --> UC004
    Customer --> UC005
    Customer --> UC006
    Customer --> UC007
    Customer --> UC008
    Customer --> UC009
    Customer --> UC010

    %% Operator Relationships
    Operator --> UC011
    Operator --> UC012
    Operator --> UC013
    Operator --> UC014
    Operator --> UC015
    Operator --> UC016
    Operator --> UC017

    %% Admin Relationships
    Admin --> UC018
    Admin --> UC019
    Admin --> UC020
    Admin --> UC021
    Admin --> UC022
    Admin --> UC023
    Admin --> UC024

    %% System Relationships
    System --> UC025
    System --> UC026
    System --> UC027
    System --> UC028
    System --> UC029

    %% External System Relationships
    PaymentGW --> UC005
    PaymentGW --> UC027
    NotificationSvc --> UC010
    NotificationSvc --> UC026

    %% Include/Extend Relationships
    UC005 -.->|includes| UC027
    UC004 -.->|includes| UC009
    UC014 -.->|extends| UC004
    UC025 -.->|extends| UC009
    UC026 -.->|extends| UC010

    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:1px
    classDef system fill:#fff3e0,stroke:#e65100,stroke-width:1px

    class Customer,Operator,Admin,PaymentGW,NotificationSvc,System actor
    class UC001,UC002,UC003,UC004,UC005,UC006,UC007,UC008,UC009,UC010,UC011,UC012,UC013,UC014,UC015,UC016,UC017,UC018,UC019,UC020,UC021,UC022,UC023,UC024 usecase
    class UC025,UC026,UC027,UC028,UC029 system
```

## Actor Descriptions

### Primary Actors

#### 👤 Customer
- **Role:** End users who book bus tickets
- **Responsibilities:** Search buses, book tickets, make payments, manage bookings
- **Access Level:** Limited to personal bookings and public information

#### 🏢 Bus Operator
- **Role:** Bus company representatives
- **Responsibilities:** Manage fleet, create schedules, handle counter bookings
- **Access Level:** Full access to own buses and bookings, limited system access

#### 👨‍💼 Admin
- **Role:** System administrators
- **Responsibilities:** Platform management, user oversight, system configuration
- **Access Level:** Full system access across all operators and users

### Secondary Actors

#### 💳 Payment Gateway
- **Role:** External payment processing services (eSewa, Khalti)
- **Responsibilities:** Process payments, return transaction status
- **Integration:** API-based integration for payment processing

#### 📧 Notification Service
- **Role:** Email and SMS service providers
- **Responsibilities:** Send booking confirmations, reminders, alerts
- **Integration:** SMTP for emails, SMS gateway for text messages

#### ⚙️ System
- **Role:** Automated system processes
- **Responsibilities:** Background tasks, scheduled operations, data maintenance
- **Functions:** Timer-based operations, data synchronization

## Use Case Relationships

### Include Relationships
- **UC-005 (Make Payment)** includes **UC-027 (Process Payments)**
- **UC-004 (Select Seats)** includes **UC-009 (Reserve Seats)**

### Extend Relationships
- **UC-014 (Counter Booking)** extends **UC-004 (Select Seats)**
- **UC-025 (Auto-expire Reservations)** extends **UC-009 (Reserve Seats)**
- **UC-026 (Send Notifications)** extends **UC-010 (Receive Notifications)**

### Dependency Relationships
- Payment use cases depend on Payment Gateway availability
- Notification use cases depend on Notification Service availability
- All booking operations depend on real-time seat availability updates

## Use Case Priorities

### High Priority (Must Have)
- UC-001: Register/Login
- UC-002: Search Buses
- UC-004: Select Seats
- UC-005: Make Payment
- UC-006: Download Ticket
- UC-011: Manage Bus Fleet
- UC-012: Create Schedules
- UC-014: Counter Booking

### Medium Priority (Should Have)
- UC-007: View Booking History
- UC-008: Cancel Booking
- UC-009: Reserve Seats
- UC-013: View Bookings
- UC-015: Manage Seat Availability
- UC-018: Manage Operators
- UC-020: Monitor Bookings

### Low Priority (Could Have)
- UC-010: Receive Notifications
- UC-016: Generate Reports
- UC-021: System Reports
- UC-024: Festival Mode
- UC-025: Auto-expire Reservations
- UC-026: Send Notifications

## Use Case Traceability Matrix

| Use Case | Functional Requirement | Business Rule | Test Case |
|----------|----------------------|---------------|-----------|
| UC-001 | FR-001, FR-002 | BR-011, BR-013 | TC-001 |
| UC-002 | FR-010 | BR-003 | TC-002 |
| UC-004 | FR-011, FR-012 | BR-001, BR-010 | TC-004 |
| UC-005 | FR-016, FR-017, FR-018 | - | TC-005 |
| UC-009 | FR-013 | BR-002 | TC-009 |
| UC-011 | FR-005, FR-006 | BR-012 | TC-011 |
| UC-012 | FR-008, FR-009 | BR-012 | TC-012 |
| UC-014 | FR-015 | BR-004, BR-005 | TC-014 |

## Business Value Analysis

### Customer Value
- **Convenience:** 24/7 online booking capability
- **Transparency:** Real-time seat availability and pricing
- **Flexibility:** Multiple payment options and booking management

### Operator Value
- **Efficiency:** Automated booking management and reporting
- **Revenue:** Increased bookings through online presence
- **Control:** Real-time fleet and schedule management

### Business Value
- **Scalability:** Support for multiple operators and routes
- **Revenue:** Commission-based business model
- **Market:** Digitization of Nepal's bus transportation sector

## Risk Analysis

### High Risk Use Cases
- **UC-005 (Make Payment):** Payment gateway failures, security risks
- **UC-025 (Auto-expire Reservations):** System timing issues, data consistency

### Medium Risk Use Cases
- **UC-004 (Select Seats):** Concurrent booking conflicts
- **UC-014 (Counter Booking):** Manual entry errors, system synchronization

### Mitigation Strategies
- Implement robust error handling and retry mechanisms
- Use database transactions for critical operations
- Implement comprehensive logging and monitoring
- Regular testing of payment gateway integrations
