# 🔄 Process Modeling & Data Flow Diagrams - BookNGO Bus Booking System

## 1. Process Overview

The BookNGO system consists of several interconnected processes that handle user management, bus operations, booking management, and payment processing. This document presents the system's data flow through various levels of Data Flow Diagrams (DFD).

## 2. Context Diagram (Level 0 DFD)

The context diagram shows the system as a single process with external entities and data flows.

```mermaid
graph TB
    %% External Entities
    Customer[👤 Customer]
    Operator[🏢 Bus Operator]
    Admin[👨‍💼 Admin]
    PaymentGW[💳 Payment Gateway]
    EmailSMS[📧 Email/SMS Service]

    %% Main System
    BookNGO[📱 BookNGO Bus Booking System]

    %% Data Flows - Customer
    Customer -->|Search Requests| BookNGO
    Customer -->|Booking Requests| BookNGO
    Customer -->|Payment Information| BookNGO
    BookNGO -->|Bus Information| Customer
    BookNGO -->|Booking Confirmations| Customer
    BookNGO -->|Tickets| Customer

    %% Data Flows - Operator
    Operator -->|Bus Details| BookNGO
    Operator -->|Schedule Information| BookNGO
    Operator -->|Counter Bookings| BookNGO
    BookNGO -->|Booking Reports| Operator
    BookNGO -->|Revenue Information| Operator

    %% Data Flows - Admin
    Admin -->|System Configuration| BookNGO
    Admin -->|User Management| BookNGO
    BookNGO -->|System Reports| Admin
    BookNGO -->|Analytics Data| Admin

    %% Data Flows - Payment Gateway
    BookNGO -->|Payment Requests| PaymentGW
    PaymentGW -->|Payment Status| BookNGO

    %% Data Flows - Email/SMS
    BookNGO -->|Notifications| EmailSMS
    EmailSMS -->|Delivery Status| BookNGO

    %% Styling
    classDef entity fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef system fill:#f3e5f5,stroke:#4a148c,stroke-width:3px

    class Customer,Operator,Admin,PaymentGW,EmailSMS entity
    class BookNGO system
```

## 3. Level 1 DFD - Major Processes

The Level 1 DFD breaks down the system into major functional processes.

```mermaid
graph TB
    %% External Entities
    Customer[👤 Customer]
    Operator[🏢 Bus Operator]
    Admin[👨‍💼 Admin]
    PaymentGW[💳 Payment Gateway]
    EmailSMS[📧 Email/SMS Service]

    %% Major Processes
    P1[1.0 User Management]
    P2[2.0 Bus & Route Management]
    P3[3.0 Schedule Management]
    P4[4.0 Booking Management]
    P5[5.0 Payment Processing]
    P6[6.0 Notification Management]
    P7[7.0 Reporting & Analytics]

    %% Data Stores
    DS1[(D1: Users)]
    DS2[(D2: Buses)]
    DS3[(D3: Routes)]
    DS4[(D4: Schedules)]
    DS5[(D5: Bookings)]
    DS6[(D6: Payments)]
    DS7[(D7: Reservations)]

    %% Customer Flows
    Customer -->|Registration/Login| P1
    Customer -->|Search Criteria| P3
    Customer -->|Booking Request| P4
    Customer -->|Payment Info| P5
    P3 -->|Available Schedules| Customer
    P4 -->|Booking Confirmation| Customer
    P6 -->|Notifications| Customer

    %% Operator Flows
    Operator -->|Bus Information| P2
    Operator -->|Schedule Data| P3
    Operator -->|Counter Booking| P4
    P7 -->|Reports| Operator

    %% Admin Flows
    Admin -->|User Data| P1
    Admin -->|System Config| P2
    P7 -->|Analytics| Admin

    %% External System Flows
    P5 -->|Payment Request| PaymentGW
    PaymentGW -->|Payment Status| P5
    P6 -->|Messages| EmailSMS

    %% Data Store Interactions
    P1 <--> DS1
    P2 <--> DS2
    P2 <--> DS3
    P3 <--> DS4
    P4 <--> DS5
    P4 <--> DS7
    P5 <--> DS6

    %% Inter-process Flows
    P1 -->|User Info| P4
    P2 -->|Bus Data| P3
    P3 -->|Schedule Info| P4
    P4 -->|Booking Data| P5
    P5 -->|Payment Status| P4
    P4 -->|Booking Info| P6
    P5 -->|Payment Data| P7

    %% Styling
    classDef entity fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef datastore fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class Customer,Operator,Admin,PaymentGW,EmailSMS entity
    class P1,P2,P3,P4,P5,P6,P7 process
    class DS1,DS2,DS3,DS4,DS5,DS6,DS7 datastore
```

## 4. Level 2 DFD - Booking Management Process

Detailed breakdown of the Booking Management process (Process 4.0).

```mermaid
graph TB
    %% External Entities
    Customer[👤 Customer]
    Operator[🏢 Bus Operator]

    %% Sub-processes
    P41[4.1 Seat Selection]
    P42[4.2 Reservation Management]
    P43[4.3 Booking Creation]
    P44[4.4 Booking Validation]
    P45[4.5 Counter Booking]

    %% Data Stores
    DS4[(D4: Schedules)]
    DS5[(D5: Bookings)]
    DS7[(D7: Reservations)]

    %% Other Processes
    P3[3.0 Schedule Management]
    P5[5.0 Payment Processing]
    P6[6.0 Notification Management]

    %% Customer Flows
    Customer -->|Seat Selection| P41
    Customer -->|Passenger Details| P43
    P41 -->|Available Seats| Customer
    P43 -->|Booking Confirmation| Customer

    %% Operator Flows
    Operator -->|Counter Booking Request| P45
    P45 -->|Booking Details| Operator

    %% Process Flows
    P41 -->|Selected Seats| P42
    P42 -->|Reserved Seats| P43
    P43 -->|Booking Data| P44
    P44 -->|Validated Booking| P5
    P45 -->|Counter Booking| P44

    %% Data Store Interactions
    P41 <--> DS4
    P42 <--> DS7
    P43 <--> DS5
    P44 <--> DS5

    %% External Process Interactions
    P3 -->|Schedule Updates| P41
    P44 -->|Booking Info| P6
    P5 -->|Payment Status| P44

    %% Styling
    classDef entity fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef subprocess fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef datastore fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class Customer,Operator entity
    class P3,P5,P6 process
    class P41,P42,P43,P44,P45 subprocess
    class DS4,DS5,DS7 datastore
```

## 5. Process Specifications

### 5.1 Process 1.0 - User Management

**Purpose:** Handle user registration, authentication, and profile management

**Inputs:**
- Registration data from customers and operators
- Login credentials
- Profile update requests from admin

**Processing:**
- Validate user credentials
- Create user accounts with appropriate roles
- Manage user sessions and permissions
- Update user profiles and settings

**Outputs:**
- User authentication tokens
- User profile information
- Account status confirmations

**Data Stores:**
- D1: Users (Read/Write)

### 5.2 Process 2.0 - Bus & Route Management

**Purpose:** Manage bus fleet information and route definitions

**Inputs:**
- Bus details from operators
- Route information from admin
- Bus type configurations

**Processing:**
- Validate bus information
- Create and update bus records
- Manage route definitions
- Configure seat layouts

**Outputs:**
- Bus availability information
- Route details for scheduling
- Seat layout configurations

**Data Stores:**
- D2: Buses (Read/Write)
- D3: Routes (Read/Write)

### 5.3 Process 3.0 - Schedule Management

**Purpose:** Create and manage bus schedules for routes

**Inputs:**
- Schedule requests from operators
- Route and bus information
- Date and time specifications

**Processing:**
- Validate schedule conflicts
- Create schedule entries
- Calculate available seats
- Update schedule status

**Outputs:**
- Available schedules for booking
- Schedule confirmations
- Seat availability updates

**Data Stores:**
- D4: Schedules (Read/Write)

### 5.4 Process 4.0 - Booking Management

**Purpose:** Handle seat reservations and booking creation

**Inputs:**
- Seat selection from customers
- Passenger details
- Counter booking requests from operators

**Processing:**
- Reserve selected seats temporarily
- Validate booking information
- Create booking records
- Manage booking status

**Outputs:**
- Booking confirmations
- Seat reservation status
- Booking details for payment

**Data Stores:**
- D5: Bookings (Read/Write)
- D7: Reservations (Read/Write)

### 5.5 Process 5.0 - Payment Processing

**Purpose:** Handle payment transactions and confirmations

**Inputs:**
- Payment information from customers
- Booking details
- Payment gateway responses

**Processing:**
- Initiate payment requests
- Validate payment responses
- Update booking payment status
- Handle payment failures

**Outputs:**
- Payment confirmations
- Transaction receipts
- Payment status updates

**Data Stores:**
- D6: Payments (Read/Write)

## 6. Data Flow Descriptions

### 6.1 Customer Booking Flow

1. **Search Request:** Customer provides source, destination, and travel date
2. **Available Schedules:** System returns matching bus schedules
3. **Seat Selection:** Customer selects available seats from layout
4. **Seat Reservation:** System temporarily reserves selected seats
5. **Passenger Details:** Customer provides passenger information
6. **Payment Processing:** Customer completes payment through gateway
7. **Booking Confirmation:** System confirms booking and generates ticket

### 6.2 Operator Schedule Flow

1. **Bus Assignment:** Operator assigns bus to route
2. **Schedule Creation:** Operator sets departure time and fare
3. **Schedule Validation:** System validates schedule conflicts
4. **Schedule Activation:** Schedule becomes available for booking
5. **Booking Monitoring:** Operator monitors bookings and revenue

### 6.3 Payment Processing Flow

1. **Payment Initiation:** System sends payment request to gateway
2. **Customer Authentication:** Gateway authenticates customer
3. **Payment Authorization:** Gateway processes payment
4. **Status Response:** Gateway returns payment status
5. **Booking Update:** System updates booking based on payment status
6. **Notification:** System sends confirmation to customer

## 7. Process Control Specifications

### 7.1 Seat Reservation Control

**Trigger:** Customer selects seats
**Condition:** Seats must be available
**Action:** Create temporary reservation with 1-hour expiry
**Exception:** If seats become unavailable, notify customer

### 7.2 Payment Timeout Control

**Trigger:** Booking created without payment
**Condition:** 1-hour timeout period
**Action:** Cancel booking and release seats
**Exception:** If payment received before timeout, confirm booking

### 7.3 Schedule Availability Control

**Trigger:** Current time reaches departure time
**Condition:** Schedule status is active
**Action:** Hide schedule from customer search
**Exception:** Allow counter booking until departure

## 8. Error Handling Processes

### 8.1 Payment Failure Handling

1. **Detection:** Payment gateway returns failure status
2. **Logging:** Record failure reason and transaction details
3. **Notification:** Inform customer of payment failure
4. **Retry:** Allow customer to retry payment
5. **Timeout:** Cancel booking if retry timeout exceeded

### 8.2 Seat Conflict Resolution

1. **Detection:** Multiple users select same seat simultaneously
2. **Priority:** First successful reservation wins
3. **Notification:** Inform other users of seat unavailability
4. **Alternative:** Suggest alternative available seats

### 8.3 System Failure Recovery

1. **Detection:** System component failure detected
2. **Isolation:** Isolate failed component
3. **Fallback:** Switch to backup systems
4. **Recovery:** Restore service and sync data
5. **Notification:** Inform users of service restoration

## 9. Performance Considerations

### 9.1 High-Volume Processing

- **Concurrent Bookings:** Handle multiple simultaneous bookings
- **Database Optimization:** Use indexing and query optimization
- **Caching:** Implement caching for frequently accessed data
- **Load Balancing:** Distribute load across multiple servers

### 9.2 Real-Time Updates

- **Seat Availability:** Real-time updates across all user sessions
- **Payment Status:** Immediate status updates after payment
- **Schedule Changes:** Instant propagation of schedule modifications

### 9.3 Scalability Design

- **Microservices:** Separate services for different processes
- **API Gateway:** Centralized API management
- **Database Sharding:** Distribute data across multiple databases
- **Auto-Scaling:** Automatic resource scaling based on demand
