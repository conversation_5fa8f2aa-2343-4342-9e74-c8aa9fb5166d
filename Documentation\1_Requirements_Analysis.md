# 📋 Requirements Analysis - BookNGO Bus Booking System

## 1. Project Overview

**Project Name:** BookNGO - Festival-Optimized Bus Ticketing System  
**Domain:** Transportation & Travel  
**Target Market:** Nepal (with focus on festival seasons like <PERSON><PERSON>, <PERSON>ihar, Chhath)  
**Technology Stack:** Laravel 11, MySQL, Blade + TailwindCSS, Payment Gateways (eSewa, Khalti)

## 2. Stakeholder Analysis

### Primary Stakeholders
- **Customers/Passengers:** End users booking bus tickets
- **Bus Operators:** Companies managing buses and schedules
- **System Administrators:** Platform managers overseeing the entire system

### Secondary Stakeholders
- **Payment Gateway Providers:** eSewa, Khalti, FonePay, IME Pay
- **Government Authorities:** Transportation regulatory bodies
- **Support Staff:** Customer service representatives

## 3. Use Case Analysis

### 3.1 Actor Identification

#### Primary Actors:
1. **Customer** - Regular users booking tickets online
2. **Bus Operator** - Bus company representatives managing their fleet
3. **Admin** - System administrators with full platform access

#### Secondary Actors:
4. **Payment Gateway** - External payment processing systems
5. **Email/SMS Service** - Notification services
6. **System** - Automated processes (seat expiry, notifications)

### 3.2 Use Case List

#### Customer Use Cases:
1. **UC-001:** Register/Login to System
2. **UC-002:** Search Available Buses
3. **UC-003:** View Bus Details and Seat Layout
4. **UC-004:** Select Seats
5. **UC-005:** Make Payment
6. **UC-006:** Download/Print Ticket
7. **UC-007:** View Booking History
8. **UC-008:** Cancel Booking
9. **UC-009:** Reserve Seats (Temporary Hold)
10. **UC-010:** Receive Notifications

#### Bus Operator Use Cases:
11. **UC-011:** Manage Bus Fleet
12. **UC-012:** Create/Manage Schedules
13. **UC-013:** View Bookings and Revenue
14. **UC-014:** Counter Booking (Manual)
15. **UC-015:** Manage Seat Availability
16. **UC-016:** Generate Reports
17. **UC-017:** Block/Unblock Seats

#### Admin Use Cases:
18. **UC-018:** Manage Operators
19. **UC-019:** Manage System Users
20. **UC-020:** Monitor All Bookings
21. **UC-021:** Generate System Reports
22. **UC-022:** Manage Routes and Cities
23. **UC-023:** System Configuration
24. **UC-024:** Festival Mode Management

#### System Use Cases:
25. **UC-025:** Auto-expire Seat Reservations
26. **UC-026:** Send Automated Notifications
27. **UC-027:** Process Payments
28. **UC-028:** Generate Booking References
29. **UC-029:** Update Seat Availability

### 3.3 Detailed Use Case Descriptions

#### UC-002: Search Available Buses
**Actor:** Customer  
**Precondition:** User is on the search page  
**Main Flow:**
1. User enters source city, destination city, and travel date
2. System validates input data
3. System searches for available schedules
4. System displays list of available buses with details
5. User can filter results by bus type, operator, departure time

**Alternative Flow:**
- 3a. No buses available: System displays "No buses found" message
- 4a. User applies filters: System updates results accordingly

**Postcondition:** User sees available bus options

#### UC-004: Select Seats
**Actor:** Customer  
**Precondition:** User has selected a bus schedule  
**Main Flow:**
1. System displays bus seat layout
2. System shows seat availability (Available/Booked/Reserved)
3. User clicks on available seats to select
4. System validates seat selection (max 10 seats)
5. System temporarily reserves selected seats
6. User proceeds to passenger details

**Alternative Flow:**
- 3a. Seat becomes unavailable: System notifies user and updates layout
- 5a. Reservation expires: System releases seats and notifies user

**Postcondition:** Seats are temporarily reserved for user

#### UC-014: Counter Booking (Manual)
**Actor:** Bus Operator  
**Precondition:** Operator is logged in and has active schedules  
**Main Flow:**
1. Operator selects schedule for booking
2. System displays seat layout
3. Operator selects available seats
4. Operator enters passenger details
5. System creates booking with "counter" type
6. System generates ticket for printing

**Alternative Flow:**
- 3a. No seats available: System shows fully booked message
- 5a. After departure time: System allows viewing only, no booking

**Postcondition:** Booking is created and ticket is ready for printing

## 4. Functional Requirements

### 4.1 User Management
- **FR-001:** System shall support user registration with email/phone verification
- **FR-002:** System shall implement role-based access control (Customer, Operator, Admin)
- **FR-003:** System shall maintain user profiles with personal and emergency contact information
- **FR-004:** System shall support password reset functionality

### 4.2 Bus and Route Management
- **FR-005:** System shall allow operators to manage their bus fleet
- **FR-006:** System shall support multiple seat layouts (2x2, 1x2, 3x2)
- **FR-007:** System shall enforce seat numbering restrictions based on layout type
- **FR-008:** System shall maintain route information with source/destination cities
- **FR-009:** System shall support intermediate stops for routes

### 4.3 Booking Management
- **FR-010:** System shall allow customers to search buses by route and date
- **FR-011:** System shall provide real-time seat availability
- **FR-012:** System shall support multiple seat booking (up to 10 seats)
- **FR-013:** System shall implement seat reservation with 1-hour expiry
- **FR-014:** System shall hide schedules after departure time from customer search
- **FR-015:** System shall allow counter booking by operators before departure

### 4.4 Payment Processing
- **FR-016:** System shall integrate with eSewa payment gateway
- **FR-017:** System shall integrate with Khalti payment gateway
- **FR-018:** System shall confirm bookings only after successful payment
- **FR-019:** System shall handle payment failures gracefully

### 4.5 Ticket Management
- **FR-020:** System shall generate PDF tickets with QR codes
- **FR-021:** System shall use compact ticket format for all bookings
- **FR-022:** System shall send tickets via email after successful booking
- **FR-023:** System shall support ticket printing for counter bookings

## 5. Non-Functional Requirements

### 5.1 Performance Requirements
- **NFR-001:** System response time shall be less than 2 seconds for search operations
- **NFR-002:** System shall support 1000+ concurrent users
- **NFR-003:** Payment processing shall complete within 30 seconds

### 5.2 Security Requirements
- **NFR-004:** System shall implement secure authentication and authorization
- **NFR-005:** System shall encrypt sensitive data in transit and at rest
- **NFR-006:** System shall comply with payment gateway security standards

### 5.3 Usability Requirements
- **NFR-007:** System shall be mobile-responsive
- **NFR-008:** System shall support English and Nepali languages
- **NFR-009:** System shall provide intuitive user interface

### 5.4 Reliability Requirements
- **NFR-010:** System shall have 99.5% uptime
- **NFR-011:** System shall implement automatic backup mechanisms
- **NFR-012:** System shall handle graceful degradation during peak loads

## 6. Business Rules

### 6.1 Booking Rules
- **BR-001:** Maximum 10 seats can be booked in a single transaction
- **BR-002:** Seat reservations expire after 1 hour without payment
- **BR-003:** Bookings close 10 minutes before departure for customers
- **BR-004:** Counter booking allowed until departure time
- **BR-005:** Only operators can create counter bookings

### 6.2 Seat Layout Rules
- **BR-006:** 2x2 layout uses seats: 25, 29, 33, 37, 41, etc.
- **BR-007:** 1x2 layout uses seats: 22, 25, 28, 31, 34, etc.
- **BR-008:** 3x2 layout uses seats: 26, 31, 36, 41, 46, etc.
- **BR-009:** Last row always has 5 seats except 1x2 layout (4 seats)
- **BR-010:** Seat numbering starts from 1, 2, 3, 4 sequentially

### 6.3 User Access Rules
- **BR-011:** Only admins can create operator accounts
- **BR-012:** Operators can only manage their own buses and schedules
- **BR-013:** Customers can only view their own bookings

## 7. Constraints

### 7.1 Technical Constraints
- **TC-001:** System must be built using Laravel 11 framework
- **TC-002:** Database must be MySQL
- **TC-003:** Frontend must use Blade templates with TailwindCSS
- **TC-004:** Payment integration limited to Nepali gateways

### 7.2 Business Constraints
- **BC-001:** System must comply with Nepal transportation regulations
- **BC-002:** Payment processing must support Nepali currency (NPR)
- **BC-003:** System must handle festival season traffic spikes

## 8. Assumptions and Dependencies

### 8.1 Assumptions
- **A-001:** Users have access to internet and email
- **A-002:** Payment gateways will maintain 99% uptime
- **A-003:** Bus operators have basic computer literacy

### 8.2 Dependencies
- **D-001:** eSewa and Khalti API availability
- **D-002:** Email service provider reliability
- **D-003:** SMS gateway for notifications
- **D-004:** Web hosting infrastructure

## 9. Success Criteria

### 9.1 Functional Success
- **SC-001:** 95% of bookings completed successfully
- **SC-002:** Payment success rate above 98%
- **SC-003:** Zero booking conflicts or double bookings

### 9.2 Performance Success
- **SC-004:** Average page load time under 2 seconds
- **SC-005:** System handles 1000 concurrent users without degradation
- **SC-006:** 99.5% system uptime during festival seasons

### 9.3 User Satisfaction
- **SC-007:** User-friendly interface with minimal training required
- **SC-008:** Positive feedback from 90% of users
- **SC-009:** Reduced manual booking errors by 80%
