{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "lint": "eslint resources/js --ext .js,.jsx,.ts,.tsx --ignore-path .gitignore --fix"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/vite": "^4.0.0", "@types/node": "^18.13.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.10", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "@vitejs/plugin-react": "^4.2.0", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.8.2", "concurrently": "^9.0.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.4", "eslint-plugin-react-hooks": "^4.6.2", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "prettier": "^3.3.0", "prettier-plugin-organize-imports": "^4.0.0", "prettier-plugin-tailwindcss": "^0.6.5", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.1.0", "typescript": "^5.0.2", "vite": "^6.2.4"}, "dependencies": {"laravel-echo": "^2.1.6", "pusher-js": "^8.4.0"}}