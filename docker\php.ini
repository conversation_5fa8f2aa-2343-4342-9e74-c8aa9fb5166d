; BookNGO Production PHP Configuration

; Performance settings
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000

; File upload settings
upload_max_filesize = 20M
post_max_size = 20M
file_uploads = On

; Session settings
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = 1
session.cookie_httponly = 1
session.cookie_samesite = "Strict"

; Security settings
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

; OPcache settings
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1
opcache.validate_timestamps = 0

; Realpath cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Date settings
date.timezone = "Asia/Kathmandu"

; MySQL settings
mysqli.default_socket = /var/run/mysqld/mysqld.sock

; Redis settings
redis.session.locking_enabled = 1
redis.session.lock_expire = 60
redis.session.lock_wait_time = 50000

; Error reporting
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
log_errors_max_len = 1024

; Resource limits
max_file_uploads = 20
default_socket_timeout = 60

; Output buffering
output_buffering = 4096
implicit_flush = Off

; Character encoding
default_charset = "UTF-8"

; Zend engine
zend.exception_ignore_args = On
zend.exception_string_param_max_len = 15
