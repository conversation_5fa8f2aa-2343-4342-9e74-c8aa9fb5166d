/* Real-time Seat Map Styles */

.seat-map-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.seat-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.seat-grid {
    display: grid;
    gap: 8px;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
}

.seat {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    position: relative;
    user-select: none;
}

.seat:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.seat.available {
    background: #10b981;
    color: white;
    border-color: #059669;
}

.seat.available:hover {
    background: #059669;
    border-color: #047857;
}

.seat.selected {
    background: #eab308;
    color: white;
    border-color: #ca8a04;
    animation: pulse 1.5s infinite;
}

.seat.selected:hover {
    background: #ca8a04;
    border-color: #a16207;
}

.seat.booked {
    background: #ef4444;
    color: white;
    border-color: #dc2626;
    cursor: not-allowed;
}

.seat.booked:hover {
    transform: none;
    box-shadow: none;
}

.seat.reserved {
    background: #3b82f6;
    color: white;
    border-color: #1d4ed8;
    cursor: not-allowed;
}

.seat.reserved:hover {
    transform: none;
    box-shadow: none;
}

/* Pulse animation for selected seats */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Real-time update notification */
.seat-update-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #1f2937;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease, fadeOut 0.3s ease 2.7s;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Seat selection info */
.seat-selection-info {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    border: 1px solid #e5e7eb;
}

.selected-seats-display {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.available-seats-counter {
    font-size: 14px;
    color: #6b7280;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .seat-map-container {
        padding: 15px;
        margin: 10px;
    }

    .seat-legend {
        gap: 15px;
        margin-bottom: 15px;
    }

    .legend-item {
        font-size: 12px;
    }

    .bus-layout-container {
        padding: 10px;
    }

    .bus-frame {
        padding: 10px;
        border-width: 2px;
    }

    .bus-top-section {
        padding: 8px 10px;
        margin-bottom: 10px;
    }

    .bus-door, .driver-seat {
        font-size: 20px;
        min-width: 40px;
        height: 35px;
    }

    .seat {
        width: 32px;
        height: 32px;
        font-size: 10px;
    }

    .seat.back-row-seat {
        width: 28px;
        height: 28px;
        font-size: 9px;
    }

    .aisle-space {
        width: 20px;
        min-height: 32px;
    }

    .seat-update-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        font-size: 13px;
        padding: 10px 14px;
    }
}

@media (max-width: 480px) {
    .seat-legend {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .seat-grid {
        gap: 4px;
        padding: 10px;
    }

    .seat {
        width: 28px;
        height: 28px;
        font-size: 9px;
    }

    .aisle-space {
        width: 20px;
        min-height: 28px;
    }

    .bus-layout-container {
        padding: 8px;
        max-width: 100%;
    }

    .bus-frame {
        padding: 8px;
        border-width: 2px;
    }

    .seat-row {
        gap: 3px;
    }

    .back-row-container {
        gap: 2px;
    }

    .bus-door, .driver-seat {
        font-size: 16px;
        min-width: 35px;
        height: 30px;
    }
}

/* Loading state */
.seat-map-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 16px;
    color: #6b7280;
}

.seat-map-loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error state */
.seat-map-error {
    text-align: center;
    padding: 40px 20px;
    color: #ef4444;
    font-size: 16px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
}

/* Enhanced Bus layout visualization */
.bus-layout-container {
    max-width: 700px;
    margin: 0 auto;
    padding: 20px;
}

.bus-frame {
    background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
    border: 3px solid #475569;
    border-radius: 25px;
    padding: 15px;
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.bus-top-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: linear-gradient(to right, #64748b, #475569);
    border-radius: 15px;
    color: white;
}

.bus-door {
    font-size: 24px;
    padding: 8px;
    background: rgba(34, 197, 94, 0.2);
    border-radius: 8px;
    border: 2px solid #22c55e;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    height: 40px;
}

.bus-front-space {
    flex: 1;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    color: #e2e8f0;
}

.bus-front-space::after {
    content: "FRONT";
    opacity: 0.7;
}

.driver-seat {
    font-size: 24px;
    padding: 8px;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    border: 2px solid #3b82f6;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    height: 40px;
}

.main-seating-area {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.seat-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 6px;
    padding: 4px 0;
}

.regular-row {
    border-bottom: 1px solid rgba(203, 213, 225, 0.5);
}

.back-row {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 2px solid #64748b;
    position: relative;
}

.back-row::before {
    content: "BACK ROW";
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    background: #f8fafc;
    padding: 0 10px;
    font-size: 10px;
    font-weight: 600;
    color: #64748b;
}

.back-row-container {
    display: flex;
    justify-content: center;
    gap: 4px;
    flex-wrap: wrap;
}

.aisle-space {
    width: 30px;
    min-height: 40px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.aisle-space::after {
    content: "";
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom, transparent, #cbd5e1, transparent);
    border-radius: 1px;
}

/* Enhanced seat styling */
.seat {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    position: relative;
    user-select: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.seat.window-seat::before {
    content: "🪟";
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 8px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.seat.back-row-seat {
    width: 35px;
    height: 35px;
    font-size: 10px;
    margin: 0 1px;
}

.bus-layout::before {
    content: 'Front';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
}

.bus-layout::after {
    content: 'Back';
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
}

/* Accessibility improvements */
.seat:focus {
    outline: 3px solid #3b82f6;
    outline-offset: 2px;
}

.seat[aria-disabled="true"] {
    opacity: 0.6;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .seat {
        border-width: 3px;
    }
    
    .seat.available {
        background: #00aa00;
        color: #fff;
        border-color: #fff;
    }

    .seat.selected {
        background: #ffaa00;
        color: #000;
        border-color: #fff;
    }

    .seat.booked {
        background: #cc0000;
        color: #fff;
        border-color: #fff;
    }

    .seat.reserved {
        background: #0066cc;
        color: #fff;
        border-color: #fff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .seat {
        transition: none;
    }
    
    .seat:hover {
        transform: none;
    }
    
    .seat.selected {
        animation: none;
    }
    
    .seat-update-notification {
        animation: none;
    }
}
