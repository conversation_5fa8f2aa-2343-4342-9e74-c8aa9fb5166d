[mysqld]
# Performance settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Connection settings
max_connections = 200
max_connect_errors = 10000
wait_timeout = 600
interactive_timeout = 600

# Query cache
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 2M

# Slow query log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Binary logging
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7

# Character set
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci

# Security
local_infile = 0
skip_show_database

# MyISAM settings
key_buffer_size = 32M
myisam_sort_buffer_size = 8M

# Temporary tables
tmp_table_size = 32M
max_heap_table_size = 32M

# Thread settings
thread_cache_size = 8
thread_stack = 256K

# Table settings
table_open_cache = 2000
table_definition_cache = 1400

# Sort and group settings
sort_buffer_size = 2M
read_buffer_size = 128K
read_rnd_buffer_size = 256K
join_buffer_size = 128K

[mysql]
default_character_set = utf8mb4

[client]
default_character_set = utf8mb4
